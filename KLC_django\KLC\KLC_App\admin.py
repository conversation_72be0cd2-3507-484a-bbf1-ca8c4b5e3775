from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from django.db.models import Q
from django.contrib.admin import SimpleListFilter
from django.http import HttpResponse
from django.shortcuts import redirect
from django.contrib import messages
import csv
from .models import (
    Person, HallReservation, Transaction, SuggestionComplaint,
    NewsAchievement, SystemSettings, FirebaseSyncLog
)
from .firebase_sync import FirebaseSyncService


# Custom filters
class DebtStatusFilter(SimpleListFilter):
    title = 'حالة المديونية'
    parameter_name = 'debt_status'

    def lookups(self, request, model_admin):
        return (
            ('no_debt', 'بدون ديون (≤ 240)'),
            ('has_debt', 'عليه ديون (> 240)'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'no_debt':
            return queryset.filter(debts_amount_2024__lte=240)
        if self.value() == 'has_debt':
            return queryset.filter(debts_amount_2024__gt=240)


class SyncStatusFilter(SimpleListFilter):
    title = 'حالة المزامنة'
    parameter_name = 'sync_status'

    def lookups(self, request, model_admin):
        return (
            ('synced', 'متزامن'),
            ('pending', 'في الانتظار'),
            ('error', 'خطأ في المزامنة'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(sync_status=self.value())


class ReservationStatusFilter(SimpleListFilter):
    title = 'حالة الحجز'
    parameter_name = 'reservation_status'

    def lookups(self, request, model_admin):
        return (
            ('in_progress', 'قيد التنفيذ'),
            ('confirmed', 'مؤكد'),
            ('expired', 'منتهي الصلاحية'),
        )

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(reservation_status=self.value())


# Inline admin classes
class HallReservationInline(admin.TabularInline):
    model = HallReservation
    extra = 0
    readonly_fields = ('created_at', 'days_remaining', 'firebase_doc_id', 'last_synced')
    fields = ('event_type', 'start_date', 'end_date', 'reservation_status', 'admin_created', 'days_remaining')


class TransactionInline(admin.TabularInline):
    model = Transaction
    extra = 0
    readonly_fields = ('created_at', 'completed_at', 'firebase_doc_id', 'last_synced')
    fields = ('transaction_type', 'transaction_status', 'receipt_number', 'created_at', 'completed_at')


class SuggestionComplaintInline(admin.TabularInline):
    model = SuggestionComplaint
    extra = 0
    readonly_fields = ('created_at', 'firebase_doc_id', 'last_synced')
    fields = ('message', 'created_at')


# Main admin classes
@admin.register(Person)
class PersonAdmin(admin.ModelAdmin):
    list_display = ('national_id', 'name', 'formatted_debt', 'debt_status_icon', 'sync_status_icon', 'last_synced')
    list_filter = (DebtStatusFilter, SyncStatusFilter, 'created_at')
    search_fields = ('national_id', 'name')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced')
    ordering = ('name',)

    fieldsets = (
        ('معلومات المواطن', {
            'fields': ('national_id', 'name', 'debts_amount_2024')
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [HallReservationInline, TransactionInline, SuggestionComplaintInline]

    actions = ['export_to_csv', 'sync_to_firebase', 'bulk_debt_update', 'bulk_debt_settlement', 'check_transaction_eligibility']

    def formatted_debt(self, obj):
        return f"{obj.debts_amount_2024:,.2f} شيقل"
    formatted_debt.short_description = 'المديونية'

    def debt_status_icon(self, obj):
        if obj.has_debts:
            return format_html('<span style="color: red;">⚠️ عليه ديون</span>')
        return format_html('<span style="color: green;">✅ بدون ديون</span>')
    debt_status_icon.short_description = 'حالة المديونية'

    def sync_status_icon(self, obj):
        if obj.sync_status == 'synced':
            return format_html('<span style="color: green;">✅</span>')
        elif obj.sync_status == 'pending':
            return format_html('<span style="color: orange;">⏳</span>')
        else:
            return format_html('<span style="color: red;">❌</span>')
    sync_status_icon.short_description = 'المزامنة'

    def export_to_csv(self, request, queryset):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="persons.csv"'

        writer = csv.writer(response)
        writer.writerow(['رقم الهوية', 'الاسم', 'المديونية', 'تاريخ الإنشاء'])

        for person in queryset:
            writer.writerow([person.national_id, person.name, person.debts_amount_2024, person.created_at])

        return response
    export_to_csv.short_description = 'تصدير إلى CSV'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for person in queryset:
            if sync_service.sync_model_to_firebase(person, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} مواطن مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'

    def bulk_debt_settlement(self, request, queryset):
        """Bulk debt settlement action"""
        if request.POST.get('post'):
            amount = float(request.POST.get('settlement_amount', 0))
            if amount <= 0:
                messages.error(request, 'يجب إدخال مبلغ صحيح أكبر من صفر')
                return

            updated_count = 0
            for person in queryset:
                person.pay_debt(amount)
                updated_count += 1

            messages.success(request, f'تم تسديد {amount} شيقل من {updated_count} مواطن')
        else:
            # Show confirmation form
            context = {
                'title': 'تسديد ديون جماعي',
                'queryset': queryset,
                'action_checkbox_name': admin.ACTION_CHECKBOX_NAME,
            }
            return render(request, 'admin/bulk_debt_settlement.html', context)
    bulk_debt_settlement.short_description = 'تسديد ديون جماعي'

    def check_transaction_eligibility(self, request, queryset):
        """Check transaction eligibility for selected persons"""
        eligible_count = 0
        ineligible_count = 0

        for person in queryset:
            if person.can_make_transactions:
                eligible_count += 1
            else:
                ineligible_count += 1

        messages.info(
            request,
            f'مؤهل للمعاملات: {eligible_count} | غير مؤهل: {ineligible_count}'
        )
    check_transaction_eligibility.short_description = 'فحص أهلية المعاملات'


@admin.register(HallReservation)
class HallReservationAdmin(admin.ModelAdmin):
    list_display = ('person', 'event_type', 'start_date', 'end_date', 'status_icon', 'days_remaining_display', 'admin_created', 'created_at')
    list_filter = (ReservationStatusFilter, 'admin_created', SyncStatusFilter, 'created_at', 'start_date')
    search_fields = ('person__national_id', 'person__name', 'full_name', 'event_type', 'phone_number')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced', 'days_remaining')
    ordering = ('-created_at',)

    fieldsets = (
        ('معلومات الحجز', {
            'fields': ('person', 'full_name', 'phone_number', 'event_type')
        }),
        ('تواريخ الحجز', {
            'fields': ('start_date', 'end_date', 'reservation_status')
        }),
        ('معلومات إضافية', {
            'fields': ('admin_created', 'days_remaining')
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['confirm_reservations', 'cancel_reservations', 'sync_to_firebase', 'cleanup_expired', 'check_conflicts']

    def status_icon(self, obj):
        if obj.reservation_status == 'confirmed':
            return format_html('<span style="color: green;">✅ مؤكد</span>')
        elif obj.reservation_status == 'in_progress':
            if obj.is_expired:
                return format_html('<span style="color: red;">⏰ منتهي الصلاحية</span>')
            return format_html('<span style="color: orange;">⏳ قيد التنفيذ</span>')
        elif obj.reservation_status == 'cancelled':
            return format_html('<span style="color: red;">❌ ملغي</span>')
        return obj.get_reservation_status_display()
    status_icon.short_description = 'الحالة'

    def days_remaining_display(self, obj):
        if obj.reservation_status == 'confirmed':
            return '✅ مؤكد'
        days = obj.days_remaining
        if days > 0:
            return f'{days} أيام متبقية'
        return '⏰ منتهي الصلاحية'
    days_remaining_display.short_description = 'الأيام المتبقية'

    def confirm_reservations(self, request, queryset):
        updated = queryset.update(reservation_status='confirmed')
        messages.success(request, f'تم تأكيد {updated} حجز')
    confirm_reservations.short_description = 'تأكيد الحجوزات المحددة'

    def cancel_reservations(self, request, queryset):
        updated = queryset.update(reservation_status='cancelled')
        messages.success(request, f'تم إلغاء {updated} حجز')
    cancel_reservations.short_description = 'إلغاء الحجوزات المحددة'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for reservation in queryset:
            if sync_service.sync_model_to_firebase(reservation, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} حجز مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'

    def cleanup_expired(self, request, queryset):
        """Cleanup expired reservations"""
        expired_count = 0
        for reservation in queryset:
            if reservation.auto_expire_if_needed():
                expired_count += 1

        if expired_count > 0:
            messages.success(request, f'تم إنهاء صلاحية {expired_count} حجز')
        else:
            messages.info(request, 'لا توجد حجوزات منتهية الصلاحية')
    cleanup_expired.short_description = 'تنظيف الحجوزات المنتهية الصلاحية'

    def check_conflicts(self, request, queryset):
        """Check for date conflicts in selected reservations"""
        conflicts_found = 0
        for reservation in queryset:
            if reservation.check_date_conflicts():
                conflicts_found += 1

        if conflicts_found > 0:
            messages.warning(request, f'تم العثور على {conflicts_found} حجز يحتوي على تضارب في التواريخ')
        else:
            messages.success(request, 'لا توجد تضاربات في التواريخ')
    check_conflicts.short_description = 'فحص تضارب التواريخ'


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('person', 'transaction_type_display', 'status_icon', 'receipt_number', 'created_at', 'completed_at')
    list_filter = ('transaction_status', 'transaction_type', SyncStatusFilter, 'created_at')
    search_fields = ('person__national_id', 'person__name', 'full_name', 'phone_number', 'receipt_number')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced')
    ordering = ('-created_at',)

    fieldsets = (
        ('معلومات الطلب', {
            'fields': ('person', 'full_name', 'phone_number', 'transaction_type')
        }),
        ('تفاصيل الطلب', {
            'fields': ('additional_notes', 'transaction_status', 'receipt_number', 'completed_at')
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_done', 'mark_as_in_progress', 'sync_to_firebase', 'bulk_add_receipt_numbers', 'generate_statistics']

    def transaction_type_display(self, obj):
        return obj.get_transaction_type_display()
    transaction_type_display.short_description = 'نوع الطلب'

    def status_icon(self, obj):
        if obj.transaction_status == 'done':
            return format_html('<span style="color: green;">✅ مكتمل</span>')
        elif obj.transaction_status == 'in_progress':
            return format_html('<span style="color: orange;">⏳ قيد التنفيذ</span>')
        elif obj.transaction_status == 'cancelled':
            return format_html('<span style="color: red;">❌ ملغي</span>')
        return obj.get_transaction_status_display()
    status_icon.short_description = 'الحالة'

    def mark_as_done(self, request, queryset):
        updated = queryset.update(transaction_status='done', completed_at=timezone.now())
        messages.success(request, f'تم تحديد {updated} طلب كمكتمل')
    mark_as_done.short_description = 'تحديد كمكتمل'

    def mark_as_in_progress(self, request, queryset):
        updated = queryset.update(transaction_status='in_progress', completed_at=None)
        messages.success(request, f'تم تحديد {updated} طلب كقيد التنفيذ')
    mark_as_in_progress.short_description = 'تحديد كقيد التنفيذ'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for transaction in queryset:
            if sync_service.sync_model_to_firebase(transaction, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} طلب مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'

    def bulk_add_receipt_numbers(self, request, queryset):
        """Bulk add receipt numbers to completed transactions"""
        if request.POST.get('post'):
            receipt_prefix = request.POST.get('receipt_prefix', 'KLC')
            start_number = int(request.POST.get('start_number', 1))

            updated_count = 0
            for i, transaction in enumerate(queryset.filter(transaction_status='done', receipt_number__isnull=True)):
                receipt_number = f"{receipt_prefix}-{start_number + i:04d}"
                transaction.receipt_number = receipt_number
                transaction.save()
                updated_count += 1

            messages.success(request, f'تم إضافة أرقام إيصالات لـ {updated_count} طلب')
        else:
            context = {
                'title': 'إضافة أرقام إيصالات جماعية',
                'queryset': queryset.filter(transaction_status='done', receipt_number__isnull=True),
                'action_checkbox_name': admin.ACTION_CHECKBOX_NAME,
            }
            return render(request, 'admin/bulk_receipt_numbers.html', context)
    bulk_add_receipt_numbers.short_description = 'إضافة أرقام إيصالات جماعية'

    def generate_statistics(self, request, queryset):
        """Generate statistics for selected transactions"""
        from django.utils import timezone

        total = queryset.count()
        completed = queryset.filter(transaction_status='done').count()
        in_progress = queryset.filter(transaction_status='in_progress').count()
        cancelled = queryset.filter(transaction_status='cancelled').count()

        completion_rate = (completed / total * 100) if total > 0 else 0

        # Get current year stats
        current_year = timezone.now().year
        year_stats = Transaction.get_statistics(current_year)

        messages.info(
            request,
            f'إحصائيات المحددة: المجموع: {total} | مكتمل: {completed} | قيد التنفيذ: {in_progress} | ملغي: {cancelled} | معدل الإكمال: {completion_rate:.1f}%'
        )

        messages.info(
            request,
            f'إحصائيات {current_year}: المجموع: {year_stats["total"]} | معدل الإكمال: {year_stats["completion_rate"]:.1f}%'
        )
    generate_statistics.short_description = 'إنتاج إحصائيات'


@admin.register(SuggestionComplaint)
class SuggestionComplaintAdmin(admin.ModelAdmin):
    list_display = ('person', 'full_name', 'message_preview', 'created_at')
    list_filter = (SyncStatusFilter, 'created_at')
    search_fields = ('person__national_id', 'person__name', 'full_name', 'message')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced')
    ordering = ('-created_at',)

    fieldsets = (
        ('معلومات المرسل', {
            'fields': ('person', 'full_name')
        }),
        ('الرسالة', {
            'fields': ('message',)
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['sync_to_firebase']

    def message_preview(self, obj):
        return obj.message[:100] + '...' if len(obj.message) > 100 else obj.message
    message_preview.short_description = 'معاينة الرسالة'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for suggestion in queryset:
            if sync_service.sync_model_to_firebase(suggestion, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} اقتراح مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'


@admin.register(NewsAchievement)
class NewsAchievementAdmin(admin.ModelAdmin):
    list_display = ('title', 'news_type', 'is_featured_icon', 'is_approved_icon', 'published_at', 'created_by', 'created_at')
    list_filter = ('news_type', 'is_featured', 'is_approved', SyncStatusFilter, 'published_at', 'created_at')
    search_fields = ('title', 'description')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced')
    ordering = ('-published_at', '-created_at')

    fieldsets = (
        ('معلومات الخبر', {
            'fields': ('title', 'description', 'news_type')
        }),
        ('الوسائط', {
            'fields': ('image_src', 'additional_images', 'video_url')
        }),
        ('إعدادات النشر', {
            'fields': ('is_featured', 'is_approved', 'published_at', 'created_by')
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['approve_news', 'feature_news', 'unfeature_news', 'sync_to_firebase']

    def is_featured_icon(self, obj):
        if obj.is_featured:
            return format_html('<span style="color: gold;">⭐ مميز</span>')
        return '—'
    is_featured_icon.short_description = 'مميز'

    def is_approved_icon(self, obj):
        if obj.is_approved:
            return format_html('<span style="color: green;">✅ معتمد</span>')
        return format_html('<span style="color: red;">❌ غير معتمد</span>')
    is_approved_icon.short_description = 'معتمد'

    def approve_news(self, request, queryset):
        updated = queryset.update(is_approved=True)
        messages.success(request, f'تم اعتماد {updated} خبر للنشر')
    approve_news.short_description = 'اعتماد للنشر'

    def feature_news(self, request, queryset):
        # Only one news can be featured at a time
        if queryset.count() > 1:
            messages.error(request, 'يمكن تمييز خبر واحد فقط في كل مرة')
            return

        # Unfeature all other news
        NewsAchievement.objects.filter(is_featured=True).update(is_featured=False)
        # Feature selected news
        updated = queryset.update(is_featured=True)
        messages.success(request, f'تم تمييز {updated} خبر')
    feature_news.short_description = 'تمييز كخبر رئيسي'

    def unfeature_news(self, request, queryset):
        updated = queryset.update(is_featured=False)
        messages.success(request, f'تم إلغاء تمييز {updated} خبر')
    unfeature_news.short_description = 'إلغاء التمييز'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for news in queryset:
            if sync_service.sync_model_to_firebase(news, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} خبر مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'

    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ('key', 'value_preview', 'description_preview', 'last_synced')
    list_filter = (SyncStatusFilter, 'created_at')
    search_fields = ('key', 'value', 'description')
    readonly_fields = ('created_at', 'updated_at', 'firebase_doc_id', 'last_synced')
    ordering = ('key',)

    fieldsets = (
        ('الإعداد', {
            'fields': ('key', 'value', 'description')
        }),
        ('معلومات المزامنة', {
            'fields': ('firebase_doc_id', 'sync_status', 'last_synced'),
            'classes': ('collapse',)
        }),
        ('معلومات النظام', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['sync_to_firebase']

    def value_preview(self, obj):
        return obj.value[:50] + '...' if len(obj.value) > 50 else obj.value
    value_preview.short_description = 'القيمة'

    def description_preview(self, obj):
        return obj.description[:100] + '...' if len(obj.description) > 100 else obj.description
    description_preview.short_description = 'الوصف'

    def sync_to_firebase(self, request, queryset):
        sync_service = FirebaseSyncService()
        success_count = 0

        for setting in queryset:
            if sync_service.sync_model_to_firebase(setting, request.user):
                success_count += 1

        messages.success(request, f'تم مزامنة {success_count} من {queryset.count()} إعداد مع Firebase')
    sync_to_firebase.short_description = 'مزامنة مع Firebase'


@admin.register(FirebaseSyncLog)
class FirebaseSyncLogAdmin(admin.ModelAdmin):
    list_display = ('collection_name', 'sync_type', 'sync_status_icon', 'records_processed', 'success_rate_display', 'duration_display', 'started_at', 'initiated_by')
    list_filter = ('collection_name', 'sync_type', 'sync_status', 'started_at')
    search_fields = ('collection_name', 'error_message')
    readonly_fields = ('id', 'started_at', 'completed_at', 'duration', 'success_rate')
    ordering = ('-started_at',)

    fieldsets = (
        ('معلومات المزامنة', {
            'fields': ('collection_name', 'sync_type', 'sync_status', 'initiated_by')
        }),
        ('الإحصائيات', {
            'fields': ('records_processed', 'records_successful', 'records_failed', 'success_rate')
        }),
        ('التوقيت', {
            'fields': ('started_at', 'completed_at', 'duration')
        }),
        ('الأخطاء', {
            'fields': ('error_message',)
        }),
    )

    def sync_status_icon(self, obj):
        if obj.sync_status == 'completed':
            return format_html('<span style="color: green;">✅ مكتمل</span>')
        elif obj.sync_status == 'in_progress':
            return format_html('<span style="color: orange;">⏳ قيد التنفيذ</span>')
        elif obj.sync_status == 'failed':
            return format_html('<span style="color: red;">❌ فشل</span>')
        elif obj.sync_status == 'partial':
            return format_html('<span style="color: orange;">⚠️ مكتمل جزئياً</span>')
        return obj.get_sync_status_display()
    sync_status_icon.short_description = 'الحالة'

    def success_rate_display(self, obj):
        rate = obj.success_rate
        if rate == 100:
            return format_html('<span style="color: green;">{:.1f}%</span>', rate)
        elif rate >= 80:
            return format_html('<span style="color: orange;">{:.1f}%</span>', rate)
        else:
            return format_html('<span style="color: red;">{:.1f}%</span>', rate)
    success_rate_display.short_description = 'معدل النجاح'

    def duration_display(self, obj):
        if obj.duration:
            total_seconds = int(obj.duration.total_seconds())
            minutes, seconds = divmod(total_seconds, 60)
            return f'{minutes}:{seconds:02d}'
        return '—'
    duration_display.short_description = 'المدة'

    def has_add_permission(self, request):
        return False  # Sync logs are created automatically

    def has_change_permission(self, request, obj=None):
        return False  # Sync logs should not be modified


# Customize admin site
admin.site.site_header = 'نظام إدارة مجلس قروي كفر عين'
admin.site.site_title = 'مجلس قروي كفر عين'
admin.site.index_title = 'لوحة التحكم الإدارية'

# Add custom CSS for RTL support and performance optimization
admin.site.enable_nav_sidebar = False  # Disable sidebar for better performance

